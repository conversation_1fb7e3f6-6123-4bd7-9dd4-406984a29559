import 'dart:math';

import 'package:fl_chart/fl_chart.dart' as fl_charts;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:police/common/model/analyze_details_model/alarms_statistic.dart';
import 'package:police/common/model/analyze_details_model/sort.dart';
import 'package:police/common/model/analyze_details_model/village_list.dart';
import 'package:police/common/model/analyze_model.dart';
import 'package:police/common/util/logger.dart';
import 'package:police/common/widget/corner_card.dart';
import 'package:police/common/widget/pie_chart.dart';
import 'package:police/page/analyze/controller.dart';
import 'package:police/page/analyzeDetail/index.dart';
import 'package:community_charts_flutter/community_charts_flutter.dart'
    as charts;

class AnalyzeTable extends StatelessWidget {
  const AnalyzeTable(
      {super.key, required this.controller, required this.detail});

  final AnalyzeModel detail;
  final AnalyzedetailController controller;

  @override
  Widget build(BuildContext context) {
    List<AlarmsStatistic> datas = controller.details.alarmsStatistics!;
    List<AlarmsStatistic> chain = controller.chain.value.alarmsStatistics!;
    List<AlarmsStatistic> yoy = controller.yoy.value.alarmsStatistics!;

    return Column(
      children: List.generate(datas.length + 1, (index) {
        List<Sort> sort = [];
        List<Sort> chainSort = [];
        List<Sort> yoySort = [];
        List<VillageList> villages = [];

        int j = index;
        if (index > 0) {
          j = index - 1;
          sort = datas[j].sort!;
          chainSort = chain[j].sort!;
          yoySort = yoy[j].sort!;
        } else {
          villages = controller.details.villageList!;
        }

        /// 警情总数
        int total = 0;
        // 计算警情总数
        for (var element in villages) {
          total += element.alarmCount ?? 0;
        }

        return Container(
          width: double.infinity,
          margin: const EdgeInsets.only(bottom: 50),
          child: CornerCard(
            child: index > 0
                ? _buildCaseTypeTable(
                    datas, chain, yoy, j, sort, chainSort, yoySort)
                : _buildAeraCaseTable(villages, total),
          ),
        );
      }),
    );
  }

  /// 区域发案表格
  Column _buildAeraCaseTable(List<VillageList> villages, int total) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          '${_getYearMonth(detail)}警情发案区域统计表',
          style: const TextStyle(fontSize: 25, color: Colors.white),
        ),
        const SizedBox(height: 10),
        Wrap(
          spacing: 10,
          runSpacing: 10,
          children: List.generate(villages.length, (i) {
            return Column(
              children: [
                Text(
                  villages[i].name ?? '',
                  style: const TextStyle(color: Colors.white),
                ),
                Text(
                  villages[i].alarmCount.toString(),
                  style: const TextStyle(color: Colors.white),
                ),
              ],
            );
          }),
        ),
        if (total > 0)
          Column(
            children: [
              const SizedBox(height: 10),
              PieChart(
                [
                  charts.Series(
                    id: 'area',
                    data: villages,
                    domainFn: (datum, _) => datum.name ?? '',
                    measureFn: (datum, _) => datum.alarmCount,
                    labelAccessorFn: (datum, index) =>
                        (datum.alarmCount / total * 100).toStringAsFixed(1) +
                        '%',
                    colorFn: (datum, index) => charts.Color.fromHex(
                        code:
                            '#${Random().nextInt(0xffffff).toRadixString(16).padLeft(6, '0')}'),
                  )
                ],
                showBehavior: true,
                position: charts.BehaviorPosition.bottom,
              ),
            ],
          )
      ],
    );
  }

  /// 警情类型表格
  Column _buildCaseTypeTable(
    List<AlarmsStatistic> datas,
    List<AlarmsStatistic> chain,
    List<AlarmsStatistic> yoy,
    int j,
    List<Sort> sort,
    List<Sort> chainSort,
    List<Sort> yoySort,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          '${_getYearMonth(detail)}${datas[j].name}警情统计表',
          style: const TextStyle(fontSize: 25, color: Colors.white),
        ),
        const SizedBox(height: 10),
        Wrap(
          spacing: 10,
          runSpacing: 10,
          children: List.generate(sort.length + 1, (i) {
            return Container(
              width: 100,
              alignment: Alignment.center,
              child: Column(
                children: [
                  Text(
                    i == 0 ? '合计（起）' : "${sort[i - 1].name}（起）",
                    textAlign: TextAlign.center,
                    style: const TextStyle(color: Colors.white),
                  ),
                  Text(
                    i == 0
                        ? datas[j].alarmCount.toString()
                        : sort[i - 1].count.toString(),
                    style: const TextStyle(color: Colors.white),
                  ),
                ],
              ),
            );
          }),
        ),
        if (datas[j].alarmCount > 0 || chain[j].alarmCount > 0)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 50),
              Padding(
                padding: const EdgeInsets.all(10),
                child: Text(
                  '${datas[j].name}警情环比',
                  style: const TextStyle(fontSize: 20, color: Colors.white),
                ),
              ),
              SizedBox(
                height: 200,
                child: fl_charts.BarChart(
                  fl_charts.BarChartData(
                    // maxY: datas[j].alarmCount.toDouble(),
                    barGroups: _buildBarGroups(sort, chainSort),
                    borderData: fl_charts.FlBorderData(show: false),
                    gridData: const fl_charts.FlGridData(show: false),
                    titlesData: _buildBarChartTitles(sort),
                    barTouchData: _buildBarChartTouthData(),
                  ),
                ),
              ),
            ],
          ),
        if (datas[j].alarmCount > 0 || yoy[j].alarmCount > 0)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 50),
              Padding(
                padding: const EdgeInsets.all(10),
                child: Text(
                  '${datas[j].name}警情同比',
                  style: const TextStyle(fontSize: 20, color: Colors.white),
                ),
              ),
              SizedBox(
                height: 200,
                child: fl_charts.BarChart(
                  fl_charts.BarChartData(
                    // maxY: datas[j].alarmCount.toDouble(),
                    barGroups: _buildBarGroups(sort, yoySort),
                    borderData: fl_charts.FlBorderData(show: false),
                    gridData: const fl_charts.FlGridData(show: false),
                    titlesData: _buildBarChartTitles(sort),
                    barTouchData: _buildBarChartTouthData(),
                  ),
                ),
              ),
            ],
          ),
      ],
    );
  }

  /// Tooltip 样式
  fl_charts.BarTouchData _buildBarChartTouthData() {
    return fl_charts.BarTouchData(
      touchTooltipData: fl_charts.BarTouchTooltipData(
        getTooltipItem: (group, groupIndex, rod, rodIndex) =>
            fl_charts.BarTooltipItem(
          rod.toY.round().toString(),
          const TextStyle(
            color: Colors.cyan,
            fontWeight: FontWeight.bold,
          ),
        ),
        getTooltipColor: (group) => Colors.transparent,
        tooltipPadding: EdgeInsets.zero,
        tooltipMargin: 8,
      ),
    );
  }

  /// 标题样式
  fl_charts.FlTitlesData _buildBarChartTitles(List<Sort> sort) {
    return fl_charts.FlTitlesData(
        leftTitles: _hideTitles(),
        rightTitles: _hideTitles(),
        topTitles: _hideTitles(),
        bottomTitles: fl_charts.AxisTitles(
          sideTitles: fl_charts.SideTitles(
            showTitles: true,
            getTitlesWidget: (value, meta) {
              return fl_charts.SideTitleWidget(
                axisSide: meta.axisSide,
                child: Text(sort[value.toInt()].name ?? '',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                    )),
              );
            },
          ),
        ));
  }

  /// 柱状图样式
  List<fl_charts.BarChartGroupData> _buildBarGroups(
      List<Sort> sort, List<Sort> chain) {
    if (sort.length > chain.length) {
    } else if (sort.length < chain.length) {}

    return List.generate(sort.length, (index) {
      return fl_charts.BarChartGroupData(
        x: index,
        barRods: [
          fl_charts.BarChartRodData(
            toY: sort[index].count!.toDouble(),
            color: Colors.blue,
            // width: 13,
            // backDrawRodData: fl_charts
            //     .BackgroundBarChartRodData(
            //   show: true,
            //   toY: datas[j]
            //       .alarmCount
            //       .toDouble(),
            //   color:
            //       Colors.white.withOpacity(.3),
            // ),
          ),
          fl_charts.BarChartRodData(
            toY: chain[index].count!.toDouble(),
            color: Colors.cyan,
          ),
        ],
      );
    });
  }

  /// 隐藏标题
  fl_charts.AxisTitles _hideTitles() {
    return const fl_charts.AxisTitles(
      sideTitles: fl_charts.SideTitles(
        showTitles: false,
      ),
    );
  }

  /// 获取年月
  String _getYearMonth(AnalyzeModel detail) {
    String str = '';

    switch (detail.types) {
      case AnalyzeSortTypes.month:
        str = '${detail.yearMonthDay!.split('-')[1]}月份';

        break;
      case AnalyzeSortTypes.quarter:
        str =
            '(${detail.yearMonthDay!.split('-')[0]})第${controller.getQuarter(detail)}季度';

        break;
      case AnalyzeSortTypes.year:
        str = '${detail.yearMonthDay!.split('-')[0]}年';

        break;
      default:
    }

    return str;
  }
}
